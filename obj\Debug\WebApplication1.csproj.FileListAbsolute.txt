D:\web\WebApplication1\obj\Debug\WebApplication1.csproj.AssemblyReference.cache
D:\web\WebApplication1\obj\Debug\WebApplication1.csproj.CoreCompileInputs.cache
D:\tryweb\WebApplication1\bin\WebApplication1.dll.config
D:\tryweb\WebApplication1\bin\WebApplication1.dll
D:\tryweb\WebApplication1\bin\WebApplication1.pdb
D:\tryweb\WebApplication1\bin\roslyn\csc.exe
D:\tryweb\WebApplication1\bin\roslyn\csc.exe.config
D:\tryweb\WebApplication1\bin\roslyn\csc.rsp
D:\tryweb\WebApplication1\bin\roslyn\csi.exe
D:\tryweb\WebApplication1\bin\roslyn\csi.exe.config
D:\tryweb\WebApplication1\bin\roslyn\csi.rsp
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.CSharp.Core.targets
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.Managed.Core.targets
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\tryweb\WebApplication1\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\tryweb\WebApplication1\bin\roslyn\System.AppContext.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Collections.Immutable.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Console.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Globalization.Calendars.dll
D:\tryweb\WebApplication1\bin\roslyn\System.IO.Compression.dll
D:\tryweb\WebApplication1\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\tryweb\WebApplication1\bin\roslyn\System.IO.FileSystem.dll
D:\tryweb\WebApplication1\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Net.Http.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Net.Sockets.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Reflection.Metadata.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\tryweb\WebApplication1\bin\roslyn\System.ValueTuple.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Xml.ReaderWriter.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Xml.XmlDocument.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Xml.XPath.dll
D:\tryweb\WebApplication1\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\tryweb\WebApplication1\bin\roslyn\vbc.exe
D:\tryweb\WebApplication1\bin\roslyn\vbc.exe.config
D:\tryweb\WebApplication1\bin\roslyn\vbc.rsp
D:\tryweb\WebApplication1\bin\roslyn\VBCSCompiler.exe
D:\tryweb\WebApplication1\bin\roslyn\VBCSCompiler.exe.config
D:\tryweb\WebApplication1\bin\Antlr3.Runtime.dll
D:\tryweb\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\tryweb\WebApplication1\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\tryweb\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\tryweb\WebApplication1\bin\Microsoft.ScriptManager.MSAjax.dll
D:\tryweb\WebApplication1\bin\Microsoft.ScriptManager.WebForms.dll
D:\tryweb\WebApplication1\bin\Microsoft.Web.Infrastructure.dll
D:\tryweb\WebApplication1\bin\Newtonsoft.Json.dll
D:\tryweb\WebApplication1\bin\System.Web.Optimization.dll
D:\tryweb\WebApplication1\bin\WebGrease.dll
D:\tryweb\WebApplication1\bin\System.Web.Optimization.xml
D:\tryweb\WebApplication1\bin\Newtonsoft.Json.xml
D:\tryweb\WebApplication1\bin\Antlr3.Runtime.pdb
D:\tryweb\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\tryweb\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\tryweb\WebApplication1\bin\zh-Hans\System.Web.Optimization.resources.dll
D:\tryweb\WebApplication1\bin\zh-Hans\Microsoft.AspNet.FriendlyUrls.resources.dll
D:\tryweb\WebApplication1\obj\Debug\WebApplication1.csproj.AssemblyReference.cache
D:\tryweb\WebApplication1\obj\Debug\WebApplication1.csproj.CoreCompileInputs.cache
D:\tryweb\WebApplication1\obj\Debug\WebAppli.7D14BD69.Up2Date
D:\tryweb\WebApplication1\obj\Debug\WebApplication1.dll
D:\tryweb\WebApplication1\obj\Debug\WebApplication1.pdb
D:\NET\WebApplication1\bin\WebApplication1.dll.config
D:\NET\WebApplication1\bin\WebApplication1.dll
D:\NET\WebApplication1\bin\WebApplication1.pdb
D:\NET\WebApplication1\bin\roslyn\csc.exe
D:\NET\WebApplication1\bin\roslyn\csc.exe.config
D:\NET\WebApplication1\bin\roslyn\csc.rsp
D:\NET\WebApplication1\bin\roslyn\csi.exe
D:\NET\WebApplication1\bin\roslyn\csi.exe.config
D:\NET\WebApplication1\bin\roslyn\csi.rsp
D:\NET\WebApplication1\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.CSharp.Core.targets
D:\NET\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\NET\WebApplication1\bin\roslyn\Microsoft.Managed.Core.targets
D:\NET\WebApplication1\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\NET\WebApplication1\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\NET\WebApplication1\bin\roslyn\System.AppContext.dll
D:\NET\WebApplication1\bin\roslyn\System.Collections.Immutable.dll
D:\NET\WebApplication1\bin\roslyn\System.Console.dll
D:\NET\WebApplication1\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\NET\WebApplication1\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\NET\WebApplication1\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\NET\WebApplication1\bin\roslyn\System.Globalization.Calendars.dll
D:\NET\WebApplication1\bin\roslyn\System.IO.Compression.dll
D:\NET\WebApplication1\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\NET\WebApplication1\bin\roslyn\System.IO.FileSystem.dll
D:\NET\WebApplication1\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\NET\WebApplication1\bin\roslyn\System.Net.Http.dll
D:\NET\WebApplication1\bin\roslyn\System.Net.Sockets.dll
D:\NET\WebApplication1\bin\roslyn\System.Reflection.Metadata.dll
D:\NET\WebApplication1\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\NET\WebApplication1\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\NET\WebApplication1\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\NET\WebApplication1\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\NET\WebApplication1\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\NET\WebApplication1\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\NET\WebApplication1\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\NET\WebApplication1\bin\roslyn\System.ValueTuple.dll
D:\NET\WebApplication1\bin\roslyn\System.Xml.ReaderWriter.dll
D:\NET\WebApplication1\bin\roslyn\System.Xml.XmlDocument.dll
D:\NET\WebApplication1\bin\roslyn\System.Xml.XPath.dll
D:\NET\WebApplication1\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\NET\WebApplication1\bin\roslyn\vbc.exe
D:\NET\WebApplication1\bin\roslyn\vbc.exe.config
D:\NET\WebApplication1\bin\roslyn\vbc.rsp
D:\NET\WebApplication1\bin\roslyn\VBCSCompiler.exe
D:\NET\WebApplication1\bin\roslyn\VBCSCompiler.exe.config
D:\NET\WebApplication1\bin\Antlr3.Runtime.dll
D:\NET\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\NET\WebApplication1\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\NET\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\NET\WebApplication1\bin\Microsoft.ScriptManager.MSAjax.dll
D:\NET\WebApplication1\bin\Microsoft.ScriptManager.WebForms.dll
D:\NET\WebApplication1\bin\Microsoft.Web.Infrastructure.dll
D:\NET\WebApplication1\bin\Newtonsoft.Json.dll
D:\NET\WebApplication1\bin\System.Web.Optimization.dll
D:\NET\WebApplication1\bin\WebGrease.dll
D:\NET\WebApplication1\bin\System.Web.Optimization.xml
D:\NET\WebApplication1\bin\Newtonsoft.Json.xml
D:\NET\WebApplication1\bin\Antlr3.Runtime.pdb
D:\NET\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\NET\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\NET\WebApplication1\bin\zh-Hans\System.Web.Optimization.resources.dll
D:\NET\WebApplication1\bin\zh-Hans\Microsoft.AspNet.FriendlyUrls.resources.dll
D:\NET\WebApplication1\obj\Debug\WebApplication1.csproj.AssemblyReference.cache
D:\NET\WebApplication1\obj\Debug\WebApplication1.csproj.CoreCompileInputs.cache
D:\NET\WebApplication1\obj\Debug\WebAppli.7D14BD69.Up2Date
D:\NET\WebApplication1\obj\Debug\WebApplication1.dll
D:\NET\WebApplication1\obj\Debug\WebApplication1.pdb
D:\try\WebApplication1\obj\Debug\WebApplication1.csproj.AssemblyReference.cache
D:\try\WebApplication1\obj\Debug\WebApplication1.csproj.CoreCompileInputs.cache
D:\try\WebApplication1\obj\Debug\WebApplication1.dll
D:\try\WebApplication1\obj\Debug\WebApplication1.pdb
D:\try\WebApplication1\bin\WebApplication1.dll.config
D:\try\WebApplication1\bin\WebApplication1.dll
D:\try\WebApplication1\bin\WebApplication1.pdb
D:\try\WebApplication1\bin\roslyn\csc.exe
D:\try\WebApplication1\bin\roslyn\csc.exe.config
D:\try\WebApplication1\bin\roslyn\csc.rsp
D:\try\WebApplication1\bin\roslyn\csi.exe
D:\try\WebApplication1\bin\roslyn\csi.exe.config
D:\try\WebApplication1\bin\roslyn\csi.rsp
D:\try\WebApplication1\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.CSharp.Core.targets
D:\try\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\try\WebApplication1\bin\roslyn\Microsoft.Managed.Core.targets
D:\try\WebApplication1\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\try\WebApplication1\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\try\WebApplication1\bin\roslyn\System.AppContext.dll
D:\try\WebApplication1\bin\roslyn\System.Collections.Immutable.dll
D:\try\WebApplication1\bin\roslyn\System.Console.dll
D:\try\WebApplication1\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\try\WebApplication1\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\try\WebApplication1\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\try\WebApplication1\bin\roslyn\System.Globalization.Calendars.dll
D:\try\WebApplication1\bin\roslyn\System.IO.Compression.dll
D:\try\WebApplication1\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\try\WebApplication1\bin\roslyn\System.IO.FileSystem.dll
D:\try\WebApplication1\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\try\WebApplication1\bin\roslyn\System.Net.Http.dll
D:\try\WebApplication1\bin\roslyn\System.Net.Sockets.dll
D:\try\WebApplication1\bin\roslyn\System.Reflection.Metadata.dll
D:\try\WebApplication1\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\try\WebApplication1\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\try\WebApplication1\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\try\WebApplication1\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\try\WebApplication1\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\try\WebApplication1\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\try\WebApplication1\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\try\WebApplication1\bin\roslyn\System.ValueTuple.dll
D:\try\WebApplication1\bin\roslyn\System.Xml.ReaderWriter.dll
D:\try\WebApplication1\bin\roslyn\System.Xml.XmlDocument.dll
D:\try\WebApplication1\bin\roslyn\System.Xml.XPath.dll
D:\try\WebApplication1\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\try\WebApplication1\bin\roslyn\vbc.exe
D:\try\WebApplication1\bin\roslyn\vbc.exe.config
D:\try\WebApplication1\bin\roslyn\vbc.rsp
D:\try\WebApplication1\bin\roslyn\VBCSCompiler.exe
D:\try\WebApplication1\bin\roslyn\VBCSCompiler.exe.config
D:\try\WebApplication1\bin\Antlr3.Runtime.dll
D:\try\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\try\WebApplication1\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\try\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\try\WebApplication1\bin\Microsoft.ScriptManager.MSAjax.dll
D:\try\WebApplication1\bin\Microsoft.ScriptManager.WebForms.dll
D:\try\WebApplication1\bin\Microsoft.Web.Infrastructure.dll
D:\try\WebApplication1\bin\Newtonsoft.Json.dll
D:\try\WebApplication1\bin\System.Web.Optimization.dll
D:\try\WebApplication1\bin\WebGrease.dll
D:\try\WebApplication1\bin\System.Web.Optimization.xml
D:\try\WebApplication1\bin\Newtonsoft.Json.xml
D:\try\WebApplication1\bin\Antlr3.Runtime.pdb
D:\try\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\try\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\try\WebApplication1\bin\zh-Hans\System.Web.Optimization.resources.dll
D:\try\WebApplication1\bin\zh-Hans\Microsoft.AspNet.FriendlyUrls.resources.dll
D:\try\WebApplication1\obj\Debug\WebAppli.7D14BD69.Up2Date
D:\web\WebApplication1\bin\WebApplication1.dll.config
D:\web\WebApplication1\bin\WebApplication1.dll
D:\web\WebApplication1\bin\WebApplication1.pdb
D:\web\WebApplication1\bin\roslyn\csc.exe
D:\web\WebApplication1\bin\roslyn\csc.exe.config
D:\web\WebApplication1\bin\roslyn\csc.rsp
D:\web\WebApplication1\bin\roslyn\csi.exe
D:\web\WebApplication1\bin\roslyn\csi.exe.config
D:\web\WebApplication1\bin\roslyn\csi.rsp
D:\web\WebApplication1\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.CSharp.Core.targets
D:\web\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\web\WebApplication1\bin\roslyn\Microsoft.Managed.Core.targets
D:\web\WebApplication1\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\web\WebApplication1\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\web\WebApplication1\bin\roslyn\System.AppContext.dll
D:\web\WebApplication1\bin\roslyn\System.Collections.Immutable.dll
D:\web\WebApplication1\bin\roslyn\System.Console.dll
D:\web\WebApplication1\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\web\WebApplication1\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\web\WebApplication1\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\web\WebApplication1\bin\roslyn\System.Globalization.Calendars.dll
D:\web\WebApplication1\bin\roslyn\System.IO.Compression.dll
D:\web\WebApplication1\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\web\WebApplication1\bin\roslyn\System.IO.FileSystem.dll
D:\web\WebApplication1\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\web\WebApplication1\bin\roslyn\System.Net.Http.dll
D:\web\WebApplication1\bin\roslyn\System.Net.Sockets.dll
D:\web\WebApplication1\bin\roslyn\System.Reflection.Metadata.dll
D:\web\WebApplication1\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\web\WebApplication1\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\web\WebApplication1\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\web\WebApplication1\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\web\WebApplication1\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\web\WebApplication1\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\web\WebApplication1\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\web\WebApplication1\bin\roslyn\System.ValueTuple.dll
D:\web\WebApplication1\bin\roslyn\System.Xml.ReaderWriter.dll
D:\web\WebApplication1\bin\roslyn\System.Xml.XmlDocument.dll
D:\web\WebApplication1\bin\roslyn\System.Xml.XPath.dll
D:\web\WebApplication1\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\web\WebApplication1\bin\roslyn\vbc.exe
D:\web\WebApplication1\bin\roslyn\vbc.exe.config
D:\web\WebApplication1\bin\roslyn\vbc.rsp
D:\web\WebApplication1\bin\roslyn\VBCSCompiler.exe
D:\web\WebApplication1\bin\roslyn\VBCSCompiler.exe.config
D:\web\WebApplication1\bin\Antlr3.Runtime.dll
D:\web\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\web\WebApplication1\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\web\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\web\WebApplication1\bin\Microsoft.ScriptManager.MSAjax.dll
D:\web\WebApplication1\bin\Microsoft.ScriptManager.WebForms.dll
D:\web\WebApplication1\bin\Microsoft.Web.Infrastructure.dll
D:\web\WebApplication1\bin\Newtonsoft.Json.dll
D:\web\WebApplication1\bin\System.Web.Optimization.dll
D:\web\WebApplication1\bin\WebGrease.dll
D:\web\WebApplication1\bin\System.Web.Optimization.xml
D:\web\WebApplication1\bin\Newtonsoft.Json.xml
D:\web\WebApplication1\bin\Antlr3.Runtime.pdb
D:\web\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\web\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\web\WebApplication1\bin\zh-Hans\System.Web.Optimization.resources.dll
D:\web\WebApplication1\bin\zh-Hans\Microsoft.AspNet.FriendlyUrls.resources.dll
D:\web\WebApplication1\obj\Debug\WebAppli.7D14BD69.Up2Date
D:\web\WebApplication1\obj\Debug\WebApplication1.dll
D:\web\WebApplication1\obj\Debug\WebApplication1.pdb
D:\try\tryone\WebApplication1\bin\WebApplication1.dll.config
D:\try\tryone\WebApplication1\bin\WebApplication1.dll
D:\try\tryone\WebApplication1\bin\WebApplication1.pdb
D:\try\tryone\WebApplication1\bin\roslyn\csc.exe
D:\try\tryone\WebApplication1\bin\roslyn\csc.exe.config
D:\try\tryone\WebApplication1\bin\roslyn\csc.rsp
D:\try\tryone\WebApplication1\bin\roslyn\csi.exe
D:\try\tryone\WebApplication1\bin\roslyn\csi.exe.config
D:\try\tryone\WebApplication1\bin\roslyn\csi.rsp
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CSharp.Core.targets
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.Managed.Core.targets
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.AppContext.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Collections.Immutable.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Console.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Globalization.Calendars.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.Compression.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.FileSystem.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Net.Http.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Net.Sockets.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Reflection.Metadata.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.ValueTuple.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.ReaderWriter.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.XmlDocument.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.XPath.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\try\tryone\WebApplication1\bin\roslyn\vbc.exe
D:\try\tryone\WebApplication1\bin\roslyn\vbc.exe.config
D:\try\tryone\WebApplication1\bin\roslyn\vbc.rsp
D:\try\tryone\WebApplication1\bin\roslyn\VBCSCompiler.exe
D:\try\tryone\WebApplication1\bin\roslyn\VBCSCompiler.exe.config
D:\try\tryone\WebApplication1\bin\Antlr3.Runtime.dll
D:\try\tryone\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\try\tryone\WebApplication1\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\try\tryone\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\try\tryone\WebApplication1\bin\Microsoft.ScriptManager.MSAjax.dll
D:\try\tryone\WebApplication1\bin\Microsoft.ScriptManager.WebForms.dll
D:\try\tryone\WebApplication1\bin\Microsoft.Web.Infrastructure.dll
D:\try\tryone\WebApplication1\bin\Newtonsoft.Json.dll
D:\try\tryone\WebApplication1\bin\System.Web.Optimization.dll
D:\try\tryone\WebApplication1\bin\WebGrease.dll
D:\try\tryone\WebApplication1\bin\System.Web.Optimization.xml
D:\try\tryone\WebApplication1\bin\Newtonsoft.Json.xml
D:\try\tryone\WebApplication1\bin\Antlr3.Runtime.pdb
D:\try\tryone\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\try\tryone\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\try\tryone\WebApplication1\bin\zh-Hans\System.Web.Optimization.resources.dll
D:\try\tryone\WebApplication1\bin\zh-Hans\Microsoft.AspNet.FriendlyUrls.resources.dll
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.csproj.AssemblyReference.cache
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.csproj.CoreCompileInputs.cache
D:\try\tryone\WebApplication1\obj\Debug\WebAppli.7D14BD69.Up2Date
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.dll
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.pdb
