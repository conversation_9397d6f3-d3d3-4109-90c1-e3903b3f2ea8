D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.csproj.AssemblyReference.cache
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.csproj.CoreCompileInputs.cache
D:\try\tryone\WebApplication1\bin\WebApplication1.dll
D:\try\tryone\WebApplication1\bin\WebApplication1.pdb
D:\try\tryone\WebApplication1\bin\Antlr3.Runtime.dll
D:\try\tryone\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.dll
D:\try\tryone\WebApplication1\bin\Microsoft.AspNet.Web.Optimization.WebForms.dll
D:\try\tryone\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.dll
D:\try\tryone\WebApplication1\bin\Microsoft.ScriptManager.MSAjax.dll
D:\try\tryone\WebApplication1\bin\Microsoft.ScriptManager.WebForms.dll
D:\try\tryone\WebApplication1\bin\Microsoft.Web.Infrastructure.dll
D:\try\tryone\WebApplication1\bin\Newtonsoft.Json.dll
D:\try\tryone\WebApplication1\bin\System.Web.Optimization.dll
D:\try\tryone\WebApplication1\bin\WebGrease.dll
D:\try\tryone\WebApplication1\bin\System.Web.Optimization.xml
D:\try\tryone\WebApplication1\bin\Newtonsoft.Json.xml
D:\try\tryone\WebApplication1\bin\Antlr3.Runtime.pdb
D:\try\tryone\WebApplication1\bin\Microsoft.AspNet.FriendlyUrls.xml
D:\try\tryone\WebApplication1\bin\Microsoft.CodeDom.Providers.DotNetCompilerPlatform.xml
D:\try\tryone\WebApplication1\bin\zh-Hans\System.Web.Optimization.resources.dll
D:\try\tryone\WebApplication1\bin\zh-Hans\Microsoft.AspNet.FriendlyUrls.resources.dll
D:\try\tryone\WebApplication1\obj\Debug\WebAppli.7D14BD69.Up2Date
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.dll
D:\try\tryone\WebApplication1\obj\Debug\WebApplication1.pdb
D:\try\tryone\WebApplication1\bin\WebApplication1.dll.config
D:\try\tryone\WebApplication1\bin\roslyn\csc.exe
D:\try\tryone\WebApplication1\bin\roslyn\csc.exe.config
D:\try\tryone\WebApplication1\bin\roslyn\csc.rsp
D:\try\tryone\WebApplication1\bin\roslyn\csi.exe
D:\try\tryone\WebApplication1\bin\roslyn\csi.exe.config
D:\try\tryone\WebApplication1\bin\roslyn\csi.rsp
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.Build.Tasks.CodeAnalysis.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.CSharp.Scripting.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.Scripting.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CodeAnalysis.VisualBasic.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.CSharp.Core.targets
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.amd64.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.DiaSymReader.Native.x86.dll
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.Managed.Core.targets
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.VisualBasic.Core.targets
D:\try\tryone\WebApplication1\bin\roslyn\Microsoft.Win32.Primitives.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.AppContext.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Collections.Immutable.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Console.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Diagnostics.DiagnosticSource.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Diagnostics.FileVersionInfo.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Diagnostics.StackTrace.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Globalization.Calendars.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.Compression.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.Compression.ZipFile.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.FileSystem.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.IO.FileSystem.Primitives.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Net.Http.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Net.Sockets.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Reflection.Metadata.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Runtime.InteropServices.RuntimeInformation.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.Algorithms.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.Encoding.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.Primitives.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Security.Cryptography.X509Certificates.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Text.Encoding.CodePages.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Threading.Tasks.Extensions.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.ValueTuple.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.ReaderWriter.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.XmlDocument.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.XPath.dll
D:\try\tryone\WebApplication1\bin\roslyn\System.Xml.XPath.XDocument.dll
D:\try\tryone\WebApplication1\bin\roslyn\vbc.exe
D:\try\tryone\WebApplication1\bin\roslyn\vbc.exe.config
D:\try\tryone\WebApplication1\bin\roslyn\vbc.rsp
D:\try\tryone\WebApplication1\bin\roslyn\VBCSCompiler.exe
D:\try\tryone\WebApplication1\bin\roslyn\VBCSCompiler.exe.config
