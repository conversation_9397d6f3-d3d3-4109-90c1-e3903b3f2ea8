using System;
using System.IO;
using System.Web;
using System.Drawing;
using System.Drawing.Imaging;

/// <summary>
/// 文件上传助手类
/// </summary>
public class FileUploadHelper
{
    /// <summary>
    /// 允许的图片扩展名
    /// </summary>
    private static readonly string[] AllowedImageExtensions = { ".jpg", ".jpeg", ".png", ".gif" };
    
    /// <summary>
    /// 最大文件大小（5MB）
    /// </summary>
    private const int MaxFileSize = 5 * 1024 * 1024;
    
    /// <summary>
    /// 上传图片
    /// </summary>
    /// <param name="fileUpload">文件上传控件</param>
    /// <param name="uploadFolder">上传文件夹路径</param>
    /// <param name="maxWidth">最大宽度</param>
    /// <param name="maxHeight">最大高度</param>
    /// <returns>上传后的文件路径</returns>
    public static string UploadImage(HttpPostedFile fileUpload, string uploadFolder, int maxWidth = 300, int maxHeight = 300)
    {
        if (fileUpload == null || fileUpload.ContentLength == 0)
        {
            throw new Exception("请选择要上传的图片");
        }
        
        // 检查文件大小
        if (fileUpload.ContentLength > MaxFileSize)
        {
            throw new Exception($"文件大小不能超过 {MaxFileSize / 1024 / 1024}MB");
        }
        
        // 检查文件扩展名
        string fileExtension = Path.GetExtension(fileUpload.FileName).ToLower();
        if (Array.IndexOf(AllowedImageExtensions, fileExtension) == -1)
        {
            throw new Exception("只允许上传 JPG、JPEG、PNG 或 GIF 格式的图片");
        }
        
        // 确保上传目录存在
        string physicalPath = HttpContext.Current.Server.MapPath(uploadFolder);
        if (!Directory.Exists(physicalPath))
        {
            Directory.CreateDirectory(physicalPath);
        }
        
        // 生成唯一文件名
        string fileName = Guid.NewGuid().ToString() + fileExtension;
        string filePath = Path.Combine(physicalPath, fileName);
        
        try
        {
            // 处理图片（调整大小）
            using (Image originalImage = Image.FromStream(fileUpload.InputStream))
            {
                // 计算调整后的尺寸
                int width = originalImage.Width;
                int height = originalImage.Height;
                
                if (width > maxWidth || height > maxHeight)
                {
                    double ratio;
                    if (width > height)
                    {
                        ratio = (double)maxWidth / width;
                    }
                    else
                    {
                        ratio = (double)maxHeight / height;
                    }
                    
                    width = (int)(width * ratio);
                    height = (int)(height * ratio);
                }
                
                // 创建缩略图
                using (Bitmap resizedImage = new Bitmap(width, height))
                {
                    using (Graphics graphics = Graphics.FromImage(resizedImage))
                    {
                        graphics.CompositingQuality = System.Drawing.Drawing2D.CompositingQuality.HighQuality;
                        graphics.InterpolationMode = System.Drawing.Drawing2D.InterpolationMode.HighQualityBicubic;
                        graphics.SmoothingMode = System.Drawing.Drawing2D.SmoothingMode.HighQuality;
                        graphics.DrawImage(originalImage, 0, 0, width, height);
                    }
                    
                    // 保存图片
                    if (fileExtension == ".jpg" || fileExtension == ".jpeg")
                    {
                        resizedImage.Save(filePath, ImageFormat.Jpeg);
                    }
                    else if (fileExtension == ".png")
                    {
                        resizedImage.Save(filePath, ImageFormat.Png);
                    }
                    else if (fileExtension == ".gif")
                    {
                        resizedImage.Save(filePath, ImageFormat.Gif);
                    }
                }
            }
            
            // 返回相对路径
            return uploadFolder + "/" + fileName;
        }
        catch (Exception ex)
        {
            throw new Exception("上传图片时出错: " + ex.Message);
        }
    }
} 