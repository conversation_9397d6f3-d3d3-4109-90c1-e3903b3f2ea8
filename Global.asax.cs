﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Web;
using System.Web.Optimization;
using System.Web.Routing;
using System.Web.Security;
using System.Web.SessionState;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace WebApplication1
{
    public class Global : HttpApplication
    {
        // 用于管理定时任务的计时器
        private static Timer _appointmentTimer;
        
        void Application_Start(object sender, EventArgs e)
        {
            // 在应用程序启动时运行的代码
            RouteConfig.RegisterRoutes(RouteTable.Routes);
            BundleConfig.RegisterBundles(BundleTable.Bundles);

            // 确保安全相关的数据库表存在
            SecurityHelper.EnsureUserLoginsTableExists();
            
            // 清理过期的账号锁定
            int clearedCount = SecurityHelper.ClearExpiredLockouts();
            if (clearedCount > 0)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("应用程序启动时清理了 {0} 个过期的账号锁定", clearedCount));
            }
            
            // 启动定时任务，每小时检查一次过期未确认的预约
            StartAppointmentTimer();
        }

        protected void Application_BeginRequest()
        {
            Response.Charset = "utf-8";
            Response.ContentEncoding = Encoding.UTF8;
            
            // 每10个请求清理一次过期锁定
            Random random = new Random();
            if (random.Next(10) == 0)
            {
                SecurityHelper.ClearExpiredLockouts();
                
                // 清理过期验证码
                VerificationHelper.ClearExpiredCodes();
                
                // 同时检查并取消过期未确认的预约
                CheckAndCancelExpiredAppointments(null);
            }
        }
        
        protected void Session_Start(object sender, EventArgs e)
        {
            // 在每个新会话开始时清理过期锁定
            SecurityHelper.ClearExpiredLockouts();
        }
        
        protected void Application_End(object sender, EventArgs e)
        {
            // 停止定时任务
            StopAppointmentTimer();
        }
        
        /// <summary>
        /// 启动预约自动取消定时任务
        /// </summary>
        private void StartAppointmentTimer()
        {
            try
            {
                // 定时器间隔：10分钟（以毫秒为单位）
                int timerInterval = 10 * 60 * 1000; // 600000毫秒 = 10分钟
                
                // 创建并启动定时器，立即执行一次，然后每10分钟执行一次
                _appointmentTimer = new Timer(CheckAndCancelExpiredAppointments, null, 0, timerInterval);
                
                System.Diagnostics.Debug.WriteLine("预约自动取消定时任务已启动，间隔：10分钟");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("启动预约自动取消定时任务时出错：{0}", ex.Message));
            }
        }
        
        /// <summary>
        /// 停止预约自动取消定时任务
        /// </summary>
        private void StopAppointmentTimer()
        {
            try
            {
                if (_appointmentTimer != null)
                {
                    // 停止并释放定时器
                    _appointmentTimer.Dispose();
                    _appointmentTimer = null;
                    
                    System.Diagnostics.Debug.WriteLine("预约自动取消定时任务已停止");
                }
            }
            catch (Exception ex)
            {
﻿                System.Diagnostics.Debug.WriteLine(string.Format("停止预约自动取消定时任务时出错：{0}", ex.Message));
            }
        }
        
        /// <summary>
        /// 检查并取消过期未确认的预约
        /// </summary>
        private void CheckAndCancelExpiredAppointments(object state)
        {
            try
            {
                // 执行自动取消过期未确认预约的方法
                int cancelledCount = AppointmentManager.AutoCancelExpiredAppointments();
                
                if (cancelledCount > 0)
                {
                    System.Diagnostics.Debug.WriteLine(string.Format("定时任务：自动取消了 {0} 个过期未确认的预约", cancelledCount));
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine(string.Format("执行预约自动取消定时任务时出错：{0}", ex.Message));
            }
        }
    }
}