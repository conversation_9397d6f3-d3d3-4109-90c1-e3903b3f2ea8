using System;
using System.Data;
using System.Data.SqlClient;
using System.Configuration;  // 添加此引用
/// <summary>
/// 管理员业务逻辑类
/// </summary>
public class AdminManager
{
    /// <summary>
    /// 获取管理员ID
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <returns>管理员ID</returns>
    public static int GetAdminIDByUserID(int userID)
    {
        string query = "SELECT AdminID FROM Administrators WHERE UserID = @UserID";
        SqlParameter parameter = new SqlParameter("@UserID", userID);
        object result = DatabaseHelper.ExecuteScalar(query, parameter);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 获取系统统计信息
    /// </summary>
    /// <returns>统计信息DataTable</returns>
    public static DataTable GetSystemStatistics()
    {
        string query = @"SELECT 
                        (SELECT COUNT(1) FROM Users) AS TotalUsers,
                        (SELECT COUNT(1) FROM Users WHERE UserType = 'CarOwner') AS TotalCarOwners,
                        (SELECT COUNT(1) FROM Users WHERE UserType = 'RepairShop') AS TotalRepairShops,
                        (SELECT COUNT(1) FROM Users WHERE UserType = 'Admin') AS TotalAdmins,
                        (SELECT COUNT(1) FROM Cars) AS TotalCars,
                        (SELECT COUNT(1) FROM RepairServices) AS TotalServices,
                        (SELECT COUNT(1) FROM Appointments) AS TotalAppointments,
                        (SELECT COUNT(1) FROM Appointments WHERE Status = 'Completed') AS CompletedAppointments,
                        (SELECT COUNT(1) FROM Reviews) AS TotalReviews,
                        (SELECT AVG(CAST(Rating AS DECIMAL(3,2))) FROM Reviews) AS AverageRating";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 获取用户列表
    /// </summary>
    /// <param name="userType">用户类型，可为null</param>
    /// <param name="searchText">搜索文本，可为null</param>
    /// <returns>用户列表DataTable</returns>
    public static DataTable GetUsers(string userType = null, string searchText = null)
    {
        string query = @"SELECT UserID, Username, Email, UserType, IsActive, CreatedDate as RegisterDate 
                        FROM Users 
                        WHERE 1=1";

        if (!string.IsNullOrEmpty(userType))
        {
            query += " AND UserType = @UserType";
        }

        if (!string.IsNullOrEmpty(searchText))
        {
            query += @" AND (Username LIKE '%' + @SearchText + '%' 
                        OR Email LIKE '%' + @SearchText + '%')";
        }

        query += " ORDER BY CreatedDate DESC";

        SqlParameter[] parameters;
        if (!string.IsNullOrEmpty(userType) && !string.IsNullOrEmpty(searchText))
        {
            parameters = new SqlParameter[]
            {
                new SqlParameter("@UserType", userType),
                new SqlParameter("@SearchText", searchText)
            };
        }
        else if (!string.IsNullOrEmpty(userType))
        {
            parameters = new SqlParameter[]
            {
                new SqlParameter("@UserType", userType)
            };
        }
        else if (!string.IsNullOrEmpty(searchText))
        {
            parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", searchText)
            };
        }
        else
        {
            parameters = new SqlParameter[] { };
        }

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 获取用户详情
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <returns>用户详情DataTable</returns>
    public static DataTable GetUserDetails(int userID)
    {
        string query = "SELECT UserID, Username, Password, Email, PhoneNumber, UserType, CreatedDate as RegisterDate, LastLoginDate, IsActive FROM Users WHERE UserID = @UserID";
        SqlParameter parameter = new SqlParameter("@UserID", userID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 更新用户状态
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <param name="isActive">是否激活</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateUserStatus(int userID, bool isActive)
    {
        string query = "UPDATE Users SET IsActive = @IsActive WHERE UserID = @UserID";
        SqlParameter[] parameters =
        {
            new SqlParameter("@UserID", userID),
            new SqlParameter("@IsActive", isActive)
        };
        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 删除用户
    /// </summary>
    /// <param name="userID">用户ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteUser(int userID)
    {
        // 获取用户类型
        string typeQuery = "SELECT UserType FROM Users WHERE UserID = @UserID";
        SqlParameter typeParam = new SqlParameter("@UserID", userID);
        object typeResult = DatabaseHelper.ExecuteScalar(typeQuery, typeParam);
        
        if (typeResult == null || typeResult == DBNull.Value)
        {
            return false;
        }

        string userType = typeResult.ToString();

        // 开始事务
        using (SqlConnection connection = new SqlConnection(
     ConfigurationManager.ConnectionStrings["CarRepairServiceDB"].ConnectionString))
        {
            connection.Open();
            SqlTransaction transaction = connection.BeginTransaction();

            try
            {
                // 根据用户类型删除相关记录
                switch (userType)
                {
                    case "CarOwner":
                        // 删除车主相关记录
                        DeleteCarOwnerRecords(userID, connection, transaction);
                        break;
                    case "RepairShop":
                        // 删除维修店相关记录
                        DeleteRepairShopRecords(userID, connection, transaction);
                        break;
                    case "Admin":
                        // 删除管理员相关记录
                        DeleteAdminRecords(userID, connection, transaction);
                        break;
                }

                // 删除用户记录
                string deleteUserQuery = "DELETE FROM Users WHERE UserID = @UserID";
                SqlCommand deleteUserCmd = new SqlCommand(deleteUserQuery, connection, transaction);
                deleteUserCmd.Parameters.AddWithValue("@UserID", userID);
                deleteUserCmd.ExecuteNonQuery();

                // 提交事务
                transaction.Commit();
                return true;
            }
            catch
            {
                // 回滚事务
                transaction.Rollback();
                return false;
            }
        }
    }

    /// <summary>
    /// 删除车主相关记录
    /// </summary>
    private static void DeleteCarOwnerRecords(int userID, SqlConnection connection, SqlTransaction transaction)
    {
        // 获取车主ID
        string ownerQuery = "SELECT OwnerID FROM CarOwners WHERE UserID = @UserID";
        SqlCommand ownerCmd = new SqlCommand(ownerQuery, connection, transaction);
        ownerCmd.Parameters.AddWithValue("@UserID", userID);
        object ownerResult = ownerCmd.ExecuteScalar();
        
        if (ownerResult != null && ownerResult != DBNull.Value)
        {
            int ownerID = Convert.ToInt32(ownerResult);
            
            // 获取车辆ID列表
            string carsQuery = "SELECT CarID FROM Cars WHERE OwnerID = @OwnerID";
            SqlCommand carsCmd = new SqlCommand(carsQuery, connection, transaction);
            carsCmd.Parameters.AddWithValue("@OwnerID", ownerID);
            SqlDataReader carsReader = carsCmd.ExecuteReader();
            
            // 收集所有车辆ID
            var carIDs = new System.Collections.Generic.List<int>();
            while (carsReader.Read())
            {
                carIDs.Add(carsReader.GetInt32(0));
            }
            carsReader.Close();
            
            // 删除每辆车的预约和维修记录
            foreach (int carID in carIDs)
            {
                // 获取预约ID列表
                string appQuery = "SELECT AppointmentID FROM Appointments WHERE CarID = @CarID";
                SqlCommand appCmd = new SqlCommand(appQuery, connection, transaction);
                appCmd.Parameters.AddWithValue("@CarID", carID);
                SqlDataReader appReader = appCmd.ExecuteReader();
                
                // 收集所有预约ID
                var appointmentIDs = new System.Collections.Generic.List<int>();
                while (appReader.Read())
                {
                    appointmentIDs.Add(appReader.GetInt32(0));
                }
                appReader.Close();
                
                // 删除每个预约的维修记录和评价
                foreach (int appointmentID in appointmentIDs)
                {
                    // 获取维修记录ID
                    string recordQuery = "SELECT RecordID FROM ServiceRecords WHERE AppointmentID = @AppointmentID";
                    SqlCommand recordCmd = new SqlCommand(recordQuery, connection, transaction);
                    recordCmd.Parameters.AddWithValue("@AppointmentID", appointmentID);
                    object recordResult = recordCmd.ExecuteScalar();
                    
                    if (recordResult != null && recordResult != DBNull.Value)
                    {
                        int recordID = Convert.ToInt32(recordResult);
                        
                        // 删除评价
                        string deleteReviewQuery = "DELETE FROM Reviews WHERE ServiceRecordID = @RecordID";
                        SqlCommand deleteReviewCmd = new SqlCommand(deleteReviewQuery, connection, transaction);
                        deleteReviewCmd.Parameters.AddWithValue("@RecordID", recordID);
                        deleteReviewCmd.ExecuteNonQuery();
                        
                        // 删除维修记录
                        string deleteRecordQuery = "DELETE FROM ServiceRecords WHERE RecordID = @RecordID";
                        SqlCommand deleteRecordCmd = new SqlCommand(deleteRecordQuery, connection, transaction);
                        deleteRecordCmd.Parameters.AddWithValue("@RecordID", recordID);
                        deleteRecordCmd.ExecuteNonQuery();
                    }
                    
                    // 删除预约
                    string deleteAppQuery = "DELETE FROM Appointments WHERE AppointmentID = @AppointmentID";
                    SqlCommand deleteAppCmd = new SqlCommand(deleteAppQuery, connection, transaction);
                    deleteAppCmd.Parameters.AddWithValue("@AppointmentID", appointmentID);
                    deleteAppCmd.ExecuteNonQuery();
                }
                
                // 删除车辆
                string deleteCarQuery = "DELETE FROM Cars WHERE CarID = @CarID";
                SqlCommand deleteCarCmd = new SqlCommand(deleteCarQuery, connection, transaction);
                deleteCarCmd.Parameters.AddWithValue("@CarID", carID);
                deleteCarCmd.ExecuteNonQuery();
            }
            
            // 删除车主
            string deleteOwnerQuery = "DELETE FROM CarOwners WHERE OwnerID = @OwnerID";
            SqlCommand deleteOwnerCmd = new SqlCommand(deleteOwnerQuery, connection, transaction);
            deleteOwnerCmd.Parameters.AddWithValue("@OwnerID", ownerID);
            deleteOwnerCmd.ExecuteNonQuery();
        }
    }

    /// <summary>
    /// 删除维修店相关记录
    /// </summary>
    private static void DeleteRepairShopRecords(int userID, SqlConnection connection, SqlTransaction transaction)
    {
        // 获取维修店ID
        string shopQuery = "SELECT ShopID FROM RepairShops WHERE UserID = @UserID";
        SqlCommand shopCmd = new SqlCommand(shopQuery, connection, transaction);
        shopCmd.Parameters.AddWithValue("@UserID", userID);
        object shopResult = shopCmd.ExecuteScalar();
        
        if (shopResult != null && shopResult != DBNull.Value)
        {
            int shopID = Convert.ToInt32(shopResult);
            
            // 删除服务
            string deleteServiceQuery = "DELETE FROM RepairServices WHERE ShopID = @ShopID";
            SqlCommand deleteServiceCmd = new SqlCommand(deleteServiceQuery, connection, transaction);
            deleteServiceCmd.Parameters.AddWithValue("@ShopID", shopID);
            deleteServiceCmd.ExecuteNonQuery();
            
            // 获取预约ID列表
            string appQuery = "SELECT AppointmentID FROM Appointments WHERE ShopID = @ShopID";
            SqlCommand appCmd = new SqlCommand(appQuery, connection, transaction);
            appCmd.Parameters.AddWithValue("@ShopID", shopID);
            SqlDataReader appReader = appCmd.ExecuteReader();
            
            // 收集所有预约ID
            var appointmentIDs = new System.Collections.Generic.List<int>();
            while (appReader.Read())
            {
                appointmentIDs.Add(appReader.GetInt32(0));
            }
            appReader.Close();
            
            // 删除每个预约的维修记录和评价
            foreach (int appointmentID in appointmentIDs)
            {
                // 获取维修记录ID
                string recordQuery = "SELECT RecordID FROM ServiceRecords WHERE AppointmentID = @AppointmentID";
                SqlCommand recordCmd = new SqlCommand(recordQuery, connection, transaction);
                recordCmd.Parameters.AddWithValue("@AppointmentID", appointmentID);
                object recordResult = recordCmd.ExecuteScalar();
                
                if (recordResult != null && recordResult != DBNull.Value)
                {
                    int recordID = Convert.ToInt32(recordResult);
                    
                    // 删除评价
                    string deleteReviewQuery = "DELETE FROM Reviews WHERE ServiceRecordID = @RecordID";
                    SqlCommand deleteReviewCmd = new SqlCommand(deleteReviewQuery, connection, transaction);
                    deleteReviewCmd.Parameters.AddWithValue("@RecordID", recordID);
                    deleteReviewCmd.ExecuteNonQuery();
                    
                    // 删除维修记录
                    string deleteRecordQuery = "DELETE FROM ServiceRecords WHERE RecordID = @RecordID";
                    SqlCommand deleteRecordCmd = new SqlCommand(deleteRecordQuery, connection, transaction);
                    deleteRecordCmd.Parameters.AddWithValue("@RecordID", recordID);
                    deleteRecordCmd.ExecuteNonQuery();
                }
                
                // 删除预约
                string deleteAppQuery = "DELETE FROM Appointments WHERE AppointmentID = @AppointmentID";
                SqlCommand deleteAppCmd = new SqlCommand(deleteAppQuery, connection, transaction);
                deleteAppCmd.Parameters.AddWithValue("@AppointmentID", appointmentID);
                deleteAppCmd.ExecuteNonQuery();
            }
            
            // 删除评价
            string deleteShopReviewsQuery = "DELETE FROM Reviews WHERE ShopID = @ShopID";
            SqlCommand deleteShopReviewsCmd = new SqlCommand(deleteShopReviewsQuery, connection, transaction);
            deleteShopReviewsCmd.Parameters.AddWithValue("@ShopID", shopID);
            deleteShopReviewsCmd.ExecuteNonQuery();
            
            // 删除维修店
            string deleteShopQuery = "DELETE FROM RepairShops WHERE ShopID = @ShopID";
            SqlCommand deleteShopCmd = new SqlCommand(deleteShopQuery, connection, transaction);
            deleteShopCmd.Parameters.AddWithValue("@ShopID", shopID);
            deleteShopCmd.ExecuteNonQuery();
        }
    }

    /// <summary>
    /// 删除管理员相关记录
    /// </summary>
    private static void DeleteAdminRecords(int userID, SqlConnection connection, SqlTransaction transaction)
    {
        // 删除管理员记录
        string deleteAdminQuery = "DELETE FROM Administrators WHERE UserID = @UserID";
        SqlCommand deleteAdminCmd = new SqlCommand(deleteAdminQuery, connection, transaction);
        deleteAdminCmd.Parameters.AddWithValue("@UserID", userID);
        deleteAdminCmd.ExecuteNonQuery();
    }

    /// <summary>
    /// 获取维修店列表
    /// </summary>
    /// <param name="searchText">搜索文本，可为null</param>
    /// <returns>维修店列表DataTable</returns>
    public static DataTable GetRepairShops(string searchText = null)
    {
        string query = @"SELECT rs.ShopID, rs.ShopName, rs.Address, rs.Rating, rs.ContactPerson,
                        u.UserID, u.Username, u.Email, u.IsActive
                        FROM RepairShops rs
                        INNER JOIN Users u ON rs.UserID = u.UserID
                        WHERE u.UserType = 'RepairShop'";

        if (!string.IsNullOrEmpty(searchText))
        {
            query += @" AND (rs.ShopName LIKE '%' + @SearchText + '%' 
                        OR rs.Address LIKE '%' + @SearchText + '%'
                        OR u.Username LIKE '%' + @SearchText + '%'
                        OR u.Email LIKE '%' + @SearchText + '%')";
        }

        query += " ORDER BY rs.ShopName";

        SqlParameter[] parameters;
        if (!string.IsNullOrEmpty(searchText))
        {
            parameters = new SqlParameter[]
            {
                new SqlParameter("@SearchText", searchText)
            };
        }
        else
        {
            parameters = new SqlParameter[] { };
        }

        return DatabaseHelper.ExecuteQuery(query, parameters);
    }

    /// <summary>
    /// 获取维修店详情
    /// </summary>
    /// <param name="shopID">维修店ID</param>
    /// <returns>维修店详情DataTable</returns>
    public static DataTable GetShopDetails(int shopID)
    {
        string query = @"SELECT rs.*, u.Username, u.Email, u.PhoneNumber as Phone, u.IsActive
                        FROM RepairShops rs
                        INNER JOIN Users u ON rs.UserID = u.UserID
                        WHERE rs.ShopID = @ShopID";
        SqlParameter parameter = new SqlParameter("@ShopID", shopID);
        return DatabaseHelper.ExecuteQuery(query, parameter);
    }

    /// <summary>
    /// 获取服务类别列表
    /// </summary>
    /// <returns>服务类别列表DataTable</returns>
    public static DataTable GetServiceCategories()
    {
        string query = "SELECT CategoryID, CategoryName, Description FROM ServiceCategories ORDER BY CategoryName";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 添加服务类别
    /// </summary>
    /// <param name="categoryName">类别名称</param>
    /// <param name="description">描述</param>
    /// <returns>成功返回类别ID，失败返回-1</returns>
    public static int AddServiceCategory(string categoryName, string description)
    {
        string query = @"INSERT INTO ServiceCategories (CategoryName, Description) 
                        VALUES (@CategoryName, @Description);
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@CategoryName", categoryName),
            new SqlParameter("@Description", description ?? (object)DBNull.Value)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 更新服务类别
    /// </summary>
    /// <param name="categoryID">类别ID</param>
    /// <param name="categoryName">类别名称</param>
    /// <param name="description">描述</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateServiceCategory(int categoryID, string categoryName, string description)
    {
        string query = @"UPDATE ServiceCategories 
                        SET CategoryName = @CategoryName, Description = @Description 
                        WHERE CategoryID = @CategoryID";

        SqlParameter[] parameters =
        {
            new SqlParameter("@CategoryID", categoryID),
            new SqlParameter("@CategoryName", categoryName),
            new SqlParameter("@Description", description ?? (object)DBNull.Value)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 删除服务类别
    /// </summary>
    /// <param name="categoryID">类别ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteServiceCategory(int categoryID)
    {
        // 检查是否有服务使用此类别
        string checkQuery = "SELECT COUNT(1) FROM RepairServices WHERE CategoryID = @CategoryID";
        SqlParameter checkParam = new SqlParameter("@CategoryID", categoryID);
        int count = Convert.ToInt32(DatabaseHelper.ExecuteScalar(checkQuery, checkParam));
        
        if (count > 0)
        {
            // 有服务使用此类别，不能删除
            return false;
        }
        
        // 删除类别
        string deleteQuery = "DELETE FROM ServiceCategories WHERE CategoryID = @CategoryID";
        SqlParameter deleteParam = new SqlParameter("@CategoryID", categoryID);
        int result = DatabaseHelper.ExecuteNonQuery(deleteQuery, deleteParam);
        return result > 0;
    }

    /// <summary>
    /// 获取系统通知
    /// </summary>
    /// <returns>系统通知DataTable</returns>
    public static DataTable GetSystemNotifications()
    {
        string query = @"SELECT NotificationID, Title, Message as Content, CreatedDate, IsRead as IsActive 
                        FROM Notifications 
                        WHERE UserID = 1 
                        ORDER BY CreatedDate DESC";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 获取活跃的系统通知
    /// </summary>
    /// <returns>活跃的系统通知DataTable</returns>
    public static DataTable GetActiveSystemNotifications()
    {
        string query = @"SELECT NotificationID, Title, Message as Content, CreatedDate 
                        FROM Notifications 
                        WHERE UserID = 1 AND IsRead = 1 
                        ORDER BY CreatedDate DESC";
        return DatabaseHelper.ExecuteQuery(query);
    }

    /// <summary>
    /// 添加系统通知
    /// </summary>
    /// <param name="title">标题</param>
    /// <param name="content">内容</param>
    /// <param name="isActive">是否激活</param>
    /// <returns>成功返回通知ID，失败返回-1</returns>
    public static int AddSystemNotification(string title, string content, bool isActive)
    {
        string query = @"INSERT INTO Notifications (UserID, Title, Message, CreatedDate, IsRead) 
                        VALUES (1, @Title, @Content, GETDATE(), @IsActive);
                        SELECT SCOPE_IDENTITY()";

        SqlParameter[] parameters =
        {
            new SqlParameter("@Title", title),
            new SqlParameter("@Content", content),
            new SqlParameter("@IsActive", isActive)
        };

        object result = DatabaseHelper.ExecuteScalar(query, parameters);
        if (result != null && result != DBNull.Value)
        {
            return Convert.ToInt32(result);
        }
        return -1;
    }

    /// <summary>
    /// 更新系统通知
    /// </summary>
    /// <param name="notificationID">通知ID</param>
    /// <param name="title">标题</param>
    /// <param name="content">内容</param>
    /// <param name="isActive">是否激活</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool UpdateSystemNotification(int notificationID, string title, string content, bool isActive)
    {
        string query = @"UPDATE Notifications 
                        SET Title = @Title, Message = @Content, IsRead = @IsActive 
                        WHERE NotificationID = @NotificationID AND UserID = 1";

        SqlParameter[] parameters =
        {
            new SqlParameter("@NotificationID", notificationID),
            new SqlParameter("@Title", title),
            new SqlParameter("@Content", content),
            new SqlParameter("@IsActive", isActive)
        };

        int result = DatabaseHelper.ExecuteNonQuery(query, parameters);
        return result > 0;
    }

    /// <summary>
    /// 删除系统通知
    /// </summary>
    /// <param name="notificationID">通知ID</param>
    /// <returns>成功返回true，失败返回false</returns>
    public static bool DeleteSystemNotification(int notificationID)
    {
        string query = "DELETE FROM Notifications WHERE NotificationID = @NotificationID AND UserID = 1";
        SqlParameter parameter = new SqlParameter("@NotificationID", notificationID);
        int result = DatabaseHelper.ExecuteNonQuery(query, parameter);
        return result > 0;
    }
} 