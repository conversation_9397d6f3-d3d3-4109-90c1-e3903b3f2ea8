{"path": "d:\\try\\tryone\\WebApplication1", "desc": "这是一个汽车维修服务管理系统，基于ASP.NET Web Forms开发，具有用户管理、维修店管理、预约管理、服务记录管理等功能。系统支持车主、维修店和管理员三种角色，提供完整的汽车维修服务流程管理。", "id": "878d8d5dce204b449cc6f4a441105516", "data": {"spaceKey": "878d8d5dce204b449cc6f4a441105516", "connections": {"webIDE": "https://878d8d5dce204b449cc6f4a441105516.ap-singapore.cloudstudio.club", "preview": "https://878d8d5dce204b449cc6f4a441105516--{port}.ap-singapore.cloudstudio.club", "api": "https://878d8d5dce204b449cc6f4a441105516--api.ap-singapore.cloudstudio.club", "pty": "https://878d8d5dce204b449cc6f4a441105516--pty.ap-singapore.cloudstudio.club"}}, "config": {"api": "https://878d8d5dce204b449cc6f4a441105516--api.ap-singapore.cloudstudio.club", "pty": "https://878d8d5dce204b449cc6f4a441105516--pty.ap-singapore.cloudstudio.club", "region": "ap-shanghai", "spaceKey": "878d8d5dce204b449cc6f4a441105516"}}